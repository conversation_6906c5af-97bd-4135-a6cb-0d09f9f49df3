# Authorization Middleware

This package contains middleware for handling authorization in the events API.

## AuthorizeOwner Middleware

The `AuthorizeOwner` middleware provides a flexible way to ensure that only the owner/author of a resource can perform update or delete operations on it.

### Two Versions Available

1. **AuthorizeOwner** - Convenience function for Event entities (backward compatibility)
2. **AuthorizeOwnerGeneric[T]** - Generic version that works with any entity type

### Basic Usage

```go
import "events-api/middleware"

// Using the convenience function for events (backward compatibility)
router.PUT("/events/:id", middleware.AuthorizeOwner("Author", "user"), route.UpdateEvent)
router.DELETE("/events/:id", middleware.AuthorizeOwner("Author", "user"), route.DeleteEvent)

// Using the generic version explicitly
router.PUT("/events/:id", middleware.AuthorizeOwnerGeneric[model.Event]("Author", "user"), route.UpdateEvent)
```

### Parameters

- **T** (type parameter): The entity type (e.g., `model.Event`, `model.Post`, `model.Document`)
- **field** (string): The field name in the resource model to compare against (e.g., "Author", "Owner", "CreatedBy")
- **contextKey** (string): The key to retrieve the user identifier from the Gin context (e.g., "user", "username", "userId")

### How it works

1. **Authentication Check**: Verifies that the user is authenticated by checking if the specified context key exists
2. **Resource Lookup**: Fetches the resource by ID from the database using the generic type
3. **Ownership Verification**: Uses reflection to compare the specified field value with the user identifier from context
4. **Context Storage**: If authorized, stores the resource in context as "resource" for use in the handler

### Examples

#### Using Generic Version with Different Entity Types
```go
// For events with "Author" field
router.PUT("/events/:id", middleware.AuthorizeOwnerGeneric[model.Event]("Author", "user"), route.UpdateEvent)

// For posts with "Owner" field (hypothetical Post model)
router.PUT("/posts/:id", middleware.AuthorizeOwnerGeneric[model.Post]("Owner", "user"), route.UpdatePost)

// For documents with "CreatedBy" field (hypothetical Document model)
router.PUT("/documents/:id", middleware.AuthorizeOwnerGeneric[model.Document]("CreatedBy", "user"), route.UpdateDocument)
```

#### Different Context Keys
```go
// Using "user" context key (set by AuthMiddleware)
router.PUT("/events/:id", middleware.AuthorizeOwnerGeneric[model.Event]("Author", "user"), route.UpdateEvent)

// Using "username" context key
router.PUT("/events/:id", middleware.AuthorizeOwnerGeneric[model.Event]("Author", "username"), route.UpdateEvent)

// Using "userId" context key
router.PUT("/events/:id", middleware.AuthorizeOwnerGeneric[model.Event]("Author", "userId"), route.UpdateEvent)
```

#### Backward Compatibility
```go
// The non-generic version still works for Events (calls the generic version internally)
router.PUT("/events/:id", middleware.AuthorizeOwner("Author", "user"), route.UpdateEvent)
```

### Handler Implementation

When using this middleware, your handlers can retrieve the pre-validated resource from context:

```go
func UpdateEvent(c *gin.Context) {
    // Get the event from middleware context (already validated for ownership)
    eventInterface, exists := c.Get("resource")
    if !exists {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Resource not found in context"})
        return
    }

    event, ok := eventInterface.(model.Event)
    if !ok {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid resource type"})
        return
    }

    // Continue with your update logic...
}
```

#### Generic Handler Pattern
```go
func UpdateResource[T any](c *gin.Context) {
    // Get the resource from middleware context (already validated for ownership)
    resourceInterface, exists := c.Get("resource")
    if !exists {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Resource not found in context"})
        return
    }

    resource, ok := resourceInterface.(T)
    if !ok {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid resource type"})
        return
    }

    // Continue with your update logic...
}
```

### Error Responses

The middleware returns appropriate HTTP status codes:

- **400 Bad Request**: When resource ID is missing
- **401 Unauthorized**: When user is not authenticated
- **403 Forbidden**: When user is not the owner of the resource
- **404 Not Found**: When the resource doesn't exist
- **500 Internal Server Error**: When there are configuration issues

### Benefits

1. **Type-Safe**: Generic version provides compile-time type safety
2. **Reusable**: Can be used with any resource type and field name
3. **Flexible**: Supports different context keys for user identification
4. **Backward Compatible**: Non-generic version still available for existing code
5. **Secure**: Prevents unauthorized access to resources
6. **Clean**: Removes repetitive authorization code from handlers
7. **Testable**: Easy to unit test with different scenarios

### Creating New Entity Models

To use the generic middleware with new entity types, ensure your models have:

1. **GORM Model**: Include `gorm.Model` or equivalent ID field
2. **Ownership Field**: A string field that identifies the owner (e.g., "Author", "Owner", "CreatedBy")

```go
type Post struct {
    gorm.Model
    Title   string `json:"title"`
    Content string `json:"content"`
    Owner   string `json:"owner"`  // Ownership field
}

type Document struct {
    gorm.Model
    Name      string `json:"name"`
    Content   string `json:"content"`
    CreatedBy string `json:"created_by"`  // Ownership field
}
```
