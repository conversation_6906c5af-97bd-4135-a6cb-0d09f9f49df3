package middleware

import (
	"events-api/database"
	"events-api/model"
	"github.com/gin-gonic/gin"
	"log"
	"net/http"
	"reflect"
)

func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		tokenString := c.<PERSON>("Authorization")
		if tokenString == "" {
			c.J<PERSON>N(401, gin.H{"error": "Authentication required"})
			c.Abort()
			return
		}

		user := validateToken(tokenString)

		if user.ID == 0 {
			c.JSON(401, gin.H{"error": "Invalid token"})
			c.Abort()
			return
		}

		storeUserInContext(c, user)

		log.Println("Username: ", user.Username)

		c.Next()
	}
}

// AuthorizeOwner creates a middleware that checks if the authenticated user is the owner/author of a resource
// This is a convenience function that uses the Event model type
// Parameters:
// - field: the field name in the resource to compare against (e.g., "Author")
// - contextKey: the key to get the user identifier from gin context (e.g., "user", "username")
func AuthorizeOwner(field string) gin.HandlerFunc {
	return AuthorizeOwnerGeneric[model.Event](field)
}

// AuthorizeOwnerGeneric creates a middleware that checks if the authenticated user is the owner/author of a resource
// This generic version can work with any entity type that has GORM model fields
// Type parameter T: the entity type (e.g., model.Event, model.Post, model.Document)
// Parameters:
// - field: the field name in the resource to compare against (e.g., "Author", "Owner", "CreatedBy")
// - contextKey: the key to get the user identifier from gin context (e.g., "user", "username")
func AuthorizeOwnerGeneric[T any](field string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get the resource ID from URL parameter
		id := c.Param("id")
		if id == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Resource ID is required"})
			c.Abort()
			return
		}

		user := getUserFromContext(c)

		// Find the resource by ID using the generic type
		var resource T
		if err := database.DB.First(&resource, id).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Resource not found"})
			c.Abort()
			return
		}

		// Use reflection to get the field value from the resource
		resourceValue := reflect.ValueOf(resource)
		fieldValue := resourceValue.FieldByName(field)

		if !fieldValue.IsValid() {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid field specified"})
			c.Abort()
			return
		}

		// Convert field value to string for comparison
		fieldString := fieldValue.String()

		// Check if the user is the owner
		if fieldString != user.Username {
			c.JSON(http.StatusForbidden, gin.H{"error": "Forbidden: You can only modify your own resources"})
			c.Abort()
			return
		}

		// Store the resource in context for use in the handler
		c.Set("resource", resource)
		c.Next()
	}
}

func validateToken(token string) *model.User {
	// JWT validation logic here
	user := model.User{}
	database.DB.First(&user, "username = ?", token)

	return &user
}

func storeUserInContext(c *gin.Context, user *model.User) {
	c.Set("user", user)
}

func getUserFromContext(c *gin.Context) *model.User {
	user, exists := c.Get("user")
	if !exists {
		return nil
	}
	return user.(*model.User)
}
