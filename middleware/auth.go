package middleware

import (
	"events-api/database"
	"events-api/model"
	"events-api/util"
	"github.com/gin-gonic/gin"
	"log"
	"net/http"
	"reflect"
)

func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.<PERSON>("Authorization")
		if authHeader == "" {
			c.JSON(401, gin.H{"error": "Authentication required"})
			c.Abort()
			return
		}

		// Extract token from Authorization header
		tokenString, err := util.ExtractTokenFromHeader(authHeader)
		if err != nil {
			c.JSON(401, gin.H{"error": err.Error()})
			c.Abort()
			return
		}

		// Validate JWT token
		claims, err := util.ValidateJWT(tokenString)
		if err != nil {
			c.JSON(401, gin.H{"error": "Invalid or expired token"})
			c.Abort()
			return
		}

		// Get user from database to ensure user still exists
		var user model.User
		if err := database.DB.First(&user, claims.UID).Error; err != nil {
			c.J<PERSON>(401, gin.H{"error": "User not found"})
			c.Abort()
			return
		}

		// Store user and claims in context for use in handlers
		storeUserInContext(c, &user)
		c.Set("user_id", claims.UID)
		c.Set("user_role", claims.Role)

		log.Printf("Authenticated user: %s (ID: %d, Role: %s)", user.Username, claims.UID, claims.Role)

		c.Next()
	}
}

// RequireRole creates a middleware that checks if the authenticated user has the required role
func RequireRole(requiredRole string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userRole, exists := c.Get("user_role")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User role not found in context"})
			c.Abort()
			return
		}

		role, ok := userRole.(string)
		if !ok {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user role format"})
			c.Abort()
			return
		}

		if role != requiredRole {
			c.JSON(http.StatusForbidden, gin.H{"error": "Insufficient permissions"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireAnyRole creates a middleware that checks if the authenticated user has any of the required roles
func RequireAnyRole(requiredRoles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userRole, exists := c.Get("user_role")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User role not found in context"})
			c.Abort()
			return
		}

		role, ok := userRole.(string)
		if !ok {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user role format"})
			c.Abort()
			return
		}

		// Check if user has any of the required roles
		for _, requiredRole := range requiredRoles {
			if role == requiredRole {
				c.Next()
				return
			}
		}

		c.JSON(http.StatusForbidden, gin.H{"error": "Insufficient permissions"})
		c.Abort()
	}
}

// RequireAdmin is a convenience middleware that requires admin role
func RequireAdmin() gin.HandlerFunc {
	return RequireRole("admin")
}

// AuthorizeOwner creates a middleware that checks if the authenticated user is the owner/author of a resource
// This is a convenience function that uses the Event model type
// Parameters:
// - field: the field name in the resource to compare against (e.g., "Author")
// - contextKey: the key to get the user identifier from gin context (e.g., "user", "username")
func AuthorizeOwner(field string) gin.HandlerFunc {
	return AuthorizeOwnerGeneric[model.Event](field)
}

// AuthorizeOwnerGeneric creates a middleware that checks if the authenticated user is the owner/author of a resource
// This generic version can work with any entity type that has GORM model fields
// Type parameter T: the entity type (e.g., model.Event, model.Post, model.Document)
// Parameters:
// - field: the field name in the resource to compare against (e.g., "Author", "Owner", "CreatedBy")
// - contextKey: the key to get the user identifier from gin context (e.g., "user", "username")
func AuthorizeOwnerGeneric[T any](field string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get the resource ID from URL parameter
		id := c.Param("id")
		if id == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Resource ID is required"})
			c.Abort()
			return
		}

		user := getUserFromContext(c)

		// Find the resource by ID using the generic type
		var resource T
		if err := database.DB.First(&resource, id).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Resource not found"})
			c.Abort()
			return
		}

		// Use reflection to get the field value from the resource
		resourceValue := reflect.ValueOf(resource)
		fieldValue := resourceValue.FieldByName(field)

		if !fieldValue.IsValid() {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid field specified"})
			c.Abort()
			return
		}

		// Convert field value to string for comparison
		fieldString := fieldValue.String()

		// Check if the user is the owner
		if fieldString != user.Username {
			c.JSON(http.StatusForbidden, gin.H{"error": "Forbidden: You can only modify your own resources"})
			c.Abort()
			return
		}

		// Store the resource in context for use in the handler
		c.Set("resource", resource)
		c.Next()
	}
}

// validateToken is deprecated - use util.ValidateJWT instead
func validateToken(token string) *model.User {
	// This function is kept for backward compatibility but should not be used
	// Use util.ValidateJWT and proper JWT validation instead
	user := model.User{}
	database.DB.First(&user, "username = ?", token)
	return &user
}

func storeUserInContext(c *gin.Context, user *model.User) {
	c.Set("user", user)
}

func getUserFromContext(c *gin.Context) *model.User {
	user, exists := c.Get("user")
	if !exists {
		return nil
	}
	return user.(*model.User)
}
