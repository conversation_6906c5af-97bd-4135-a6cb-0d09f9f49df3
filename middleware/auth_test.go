package middleware

import (
	"events-api/model"
	"github.com/gin-gonic/gin"
	"net/http"
)

// Example of how to use the middleware with different parameters
func ExampleAuthorizeOwner() {
	router := gin.New()

	// Using the convenience function for Events (backward compatibility)
	router.PUT("/events/:id", AuthorizeOwner("Author", "user"), func(c *gin.Context) {
		// Handler logic here
		c.<PERSON>(http.StatusOK, gin.H{"message": "Event updated"})
	})

	// Using the generic version explicitly for Events
	router.PUT("/events/:id", AuthorizeOwnerGeneric[model.Event]("Author", "user"), func(c *gin.Context) {
		// Handler logic here
		c.JSON(http.StatusOK, gin.H{"message": "Event updated"})
	})

	// Using the generic version for other entity types (hypothetical models)
	// router.PUT("/posts/:id", AuthorizeOwnerGeneric[model.Post]("Owner", "username"), func(c *gin.Context) {
	//     // Handler logic here
	//     c.<PERSON>(http.StatusOK, gin.H{"message": "Post updated"})
	// })

	// router.DELETE("/documents/:id", AuthorizeOwnerGeneric[model.Document]("CreatedBy", "userId"), func(c *gin.Context) {
	//     // Handler logic here
	//     c.JSON(http.StatusNoContent, nil)
	// })
}
