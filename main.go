package main

import (
	"events-api/middleware"
	"events-api/route"
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
)

//TIP <p>To run your code, right-click the code and select <b>Run</b>.</p> <p>Alternatively, click
// the <icon src="AllIcons.Actions.Execute"/> icon in the gutter and select the <b>Run</b> menu item from here.</p>

func main() {

	r := gin.Default()
	r.GET("/", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message": "Events API",
		})
	})

	r.POST("/register", route.Register)
	r.POST("/login", route.Login)

	r.GET("/users", route.GetUsers)

	authenticated := r.Group("/")
	authenticated.Use(middleware.AuthMiddleware())
	authenticated.GET("/profile", route.GetProfile)

	events := r.Group("/events")
	events.GET("/", route.GetEvents)
	events.GET("/:id", route.GetEvent)

	authenticated.POST("/events", route.CreateEvent)

	authorized := authenticated.Group("/events")
	authorized.Use(middleware.AuthorizeOwner("Author"))
	authorized.PUT("/:id", middleware.AuthorizeOwner("Author"), route.UpdateEvent)
	authorized.DELETE("/:id", middleware.AuthorizeOwner("Author"), route.DeleteEvent)

	//server.Use(middleware.Logger())

	fmt.Println("Server is running on port 8080")

	r.Run(":8080")
}
