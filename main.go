package main

import (
	"events-api/middleware"
	"events-api/route"
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
)

//TIP <p>To run your code, right-click the code and select <b>Run</b>.</p> <p>Alternatively, click
// the <icon src="AllIcons.Actions.Execute"/> icon in the gutter and select the <b>Run</b> menu item from here.</p>

func main() {

	r := gin.Default()
	r.GET("/", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message": "Events API",
		})
	})

	// Public routes (no authentication required)
	r.POST("/register", route.Register)
	r.POST("/login", route.Login)

	// Public events routes (read-only)
	events := r.Group("/events")
	events.GET("/", route.GetEvents)
	events.GET("/:id", route.GetEvent)

	// Authenticated routes (JWT token required)
	authenticated := r.Group("/")
	authenticated.Use(middleware.AuthMiddleware())
	{
		authenticated.GET("/profile", route.GetProfile)
		authenticated.POST("/events", route.CreateEvent)

		// Owner-only routes (user can only modify their own resources)
		authenticated.PUT("/events/:id", middleware.AuthorizeOwner("Author"), route.UpdateEvent)
		authenticated.DELETE("/events/:id", middleware.AuthorizeOwner("Author"), route.DeleteEvent)
	}

	// Admin-only routes (require admin role)
	admin := r.Group("/admin")
	admin.Use(middleware.AuthMiddleware())
	admin.Use(middleware.RequireAdmin())
	{
		admin.GET("/users", route.GetUsers)
		// Add more admin-only routes here as needed
	}

	//server.Use(middleware.Logger())

	fmt.Println("Server is running on port 8080")

	r.Run(":8080")
}
