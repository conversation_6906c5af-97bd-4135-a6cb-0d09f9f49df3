package model

import (
	"gorm.io/gorm"
	"time"
)

// Post represents a blog post or article
type Post struct {
	gorm.Model
	Title   string    `json:"title" binding:"required"`
	Content string    `json:"content" binding:"required"`
	Owner   string    `json:"owner"`    // Ownership field for authorization
	Tags    []string  `json:"tags" gorm:"serializer:json"`
	PublishedAt *time.Time `json:"published_at,omitempty"`
}

// Document represents a document or file
type Document struct {
	gorm.Model
	Name        string `json:"name" binding:"required"`
	Content     string `json:"content"`
	ContentType string `json:"content_type"`
	CreatedBy   string `json:"created_by"`  // Ownership field for authorization
	Size        int64  `json:"size"`
	IsPublic    bool   `json:"is_public"`
}

// Task represents a task or todo item
type Task struct {
	gorm.Model
	Title       string     `json:"title" binding:"required"`
	Description string     `json:"description"`
	AssignedTo  string     `json:"assigned_to"`  // Ownership field for authorization
	Status      string     `json:"status" gorm:"default:pending"`
	Priority    string     `json:"priority" gorm:"default:medium"`
	DueDate     *time.Time `json:"due_date,omitempty"`
	CompletedAt *time.Time `json:"completed_at,omitempty"`
}

// Project represents a project
type Project struct {
	gorm.Model
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
	Author      string `json:"author"`     // Ownership field for authorization
	Status      string `json:"status" gorm:"default:active"`
	StartDate   time.Time `json:"start_date"`
	EndDate     *time.Time `json:"end_date,omitempty"`
}
