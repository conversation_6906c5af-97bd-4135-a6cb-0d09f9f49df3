package model

import "gorm.io/gorm"

type User struct {
	gorm.Model `json:"-"`
	Username   string `json:"username" gorm:"unique" binding:"required"`
	Password   string `json:"password,omitempty" binding:"required"`
	Role       string `json:"role" gorm:"default:user"`
}

// UserResponse represents the user data returned in API responses (without password)
type UserResponse struct {
	ID       uint   `json:"id"`
	Username string `json:"username"`
	Role     string `json:"role"`
}
