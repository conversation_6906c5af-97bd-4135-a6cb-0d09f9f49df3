package route

import (
	"events-api/database"
	"events-api/model"
	"events-api/util"
	"github.com/gin-gonic/gin"
)

func Register(c *gin.Context) {
	var user model.User
	if err := c.<PERSON>ind<PERSON>(&user); err != nil {
		c.J<PERSON>(400, gin.H{"error": "Invalid input"})
		return
	}
	// Hash the password before storing (e.g., using bcrypt)
	database.DB.Create(&user)
	c.J<PERSON>(201, gin.H{"message": util.Compile(`User {{.Username}} registered`, user)})
}

func Login(c *gin.Context) {
	var login model.Login
	if err := c.ShouldBindJSON(&login); err != nil {
		c.JSON(400, gin.H{"error": "Invalid input"})
		return
	}
	var user model.User
	database.DB.First(&user, "username = ?", login.Username)

	if user.Password != login.Password {
		c.J<PERSON>N(401, gin.H{"error": "Invalid credentials"})
		return
	}

	c.<PERSON>(200, gin.H{"token": user.Username, "message": util.Compile("Login successful {{.Username}} {{.ID}}", user)})
	// Check user credentials, return JWT
}

func GetProfile(c *gin.Context) {
	// Protected endpoint
	c.JSON(200, gin.H{"message": "Success"})
}

func GetUsers(c *gin.Context) {
	var users []model.User
	database.DB.Find(&users)
	c.JSON(200, users)
}
