package route

import (
	"events-api/database"
	"events-api/model"
	"events-api/util"
	"github.com/gin-gonic/gin"
)

func Register(c *gin.Context) {
	var user model.User
	if err := c.Should<PERSON>ind<PERSON>(&user); err != nil {
		c.<PERSON>(400, gin.H{"error": "Invalid input"})
		return
	}

	// Validate password requirements
	if err := util.IsPasswordValid(user.Password); err != nil {
		c.JSON(400, gin.H{"error": err.Error()})
		return
	}

	// Hash the password before storing
	hashedPassword, err := util.HashPassword(user.Password)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to process password"})
		return
	}
	user.Password = hashedPassword

	// Set default role if not provided
	if user.Role == "" {
		user.Role = "user"
	}

	// Check if username already exists
	var existingUser model.User
	if err := database.DB.Where("username = ?", user.Username).First(&existingUser).Error; err == nil {
		c.<PERSON>(409, gin.H{"error": "Username already exists"})
		return
	}

	// Create the user
	if err := database.DB.Create(&user).Error; err != nil {
		c.JSON(500, gin.H{"error": "Failed to create user"})
		return
	}

	// Create response without password
	userResponse := model.UserResponse{
		ID:       user.ID,
		Username: user.Username,
		Role:     user.Role,
	}

	c.JSON(201, gin.H{
		"message": util.Compile(`User {{.Username}} registered successfully`, user),
		"user":    userResponse,
	})
}

func Login(c *gin.Context) {
	var login model.Login
	if err := c.ShouldBindJSON(&login); err != nil {
		c.JSON(400, gin.H{"error": "Invalid input"})
		return
	}

	// Find user by username
	var user model.User
	if err := database.DB.Where("username = ?", login.Username).First(&user).Error; err != nil {
		c.JSON(401, gin.H{"error": "Invalid credentials"})
		return
	}

	// Verify password using bcrypt
	if err := util.VerifyPassword(user.Password, login.Password); err != nil {
		c.JSON(401, gin.H{"error": "Invalid credentials"})
		return
	}

	// Generate JWT token with user claims
	token, err := util.GenerateJWT(user.ID, user.Role)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to generate token"})
		return
	}

	c.JSON(200, gin.H{
		"token":   token,
		"user_id": user.ID,
		"role":    user.Role,
		"message": util.Compile("Login successful {{.Username}}", user),
	})
}

func GetProfile(c *gin.Context) {
	// Protected endpoint
	c.JSON(200, gin.H{"message": "Success"})
}

func GetUsers(c *gin.Context) {
	var users []model.User
	database.DB.Find(&users)
	c.JSON(200, users)
}
