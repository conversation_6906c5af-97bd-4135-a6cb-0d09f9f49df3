package route

import (
	"events-api/database"
	"events-api/model"
	"github.com/gin-gonic/gin"
	"net/http"
)

func CreateEvent(c *gin.Context) {
	var event model.Event
	if err := c.ShouldBindJSON(&event); err != nil {
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	username, exists := c.Get("user")

	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	event.Author = username.(*model.User).Username

	database.DB.Create(&event)
	c.<PERSON>(http.StatusCreated, event)
}

func GetEvents(c *gin.Context) {
	var events []model.Event
	database.DB.Find(&events)
	c.<PERSON>(http.StatusOK, events)
}

func GetEvent(c *gin.Context) {
	var event model.Event
	id := c.Param("id")
	if err := database.DB.First(&event, id).Error; err != nil {
		c.<PERSON>(http.StatusNotFound, gin.H{"error": "Event not found"})
		return
	}
	c.<PERSON>(http.StatusOK, event)
}

func UpdateEvent(c *gin.Context) {
	// Get the event from middleware context (already validated for ownership)
	eventInterface, exists := c.Get("resource")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Resource not found in context"})
		return
	}

	event, ok := eventInterface.(model.Event)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid resource type"})
		return
	}

	// Bind the updated data to the event
	if err := c.ShouldBindJSON(&event); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Save the updated event
	database.DB.Save(&event)
	c.JSON(http.StatusOK, event)
}

func DeleteEvent(c *gin.Context) {
	// Get the event from middleware context (already validated for ownership)
	eventInterface, exists := c.Get("resource")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Resource not found in context"})
		return
	}

	event, ok := eventInterface.(model.Event)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid resource type"})
		return
	}

	// Delete the event
	database.DB.Delete(&event)
	c.JSON(http.StatusNoContent, nil)
}
