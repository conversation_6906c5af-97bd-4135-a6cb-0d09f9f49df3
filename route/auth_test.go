package route

import (
	"bytes"
	"encoding/json"
	"events-api/database"
	"events-api/model"
	"events-api/util"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func setupTestDB() *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		panic("Failed to connect to test database")
	}
	
	db.AutoMigrate(&model.User{})
	return db
}

func TestRegister(t *testing.T) {
	// Setup test database
	database.DB = setupTestDB()
	
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.POST("/register", Register)

	tests := []struct {
		name           string
		requestBody    map[string]interface{}
		expectedStatus int
		expectedError  string
	}{
		{
			name: "Valid registration",
			requestBody: map[string]interface{}{
				"username": "testuser",
				"password": "password123",
			},
			expectedStatus: 201,
		},
		{
			name: "Invalid JSON",
			requestBody: map[string]interface{}{
				"invalid": "data",
			},
			expectedStatus: 400,
		},
		{
			name: "Password too short",
			requestBody: map[string]interface{}{
				"username": "testuser2",
				"password": "123",
			},
			expectedStatus: 400,
			expectedError:  "password must be at least 6 characters long",
		},
		{
			name: "Duplicate username",
			requestBody: map[string]interface{}{
				"username": "testuser", // Same as first test
				"password": "password123",
			},
			expectedStatus: 409,
			expectedError:  "Username already exists",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			jsonBody, _ := json.Marshal(tt.requestBody)
			req, _ := http.NewRequest("POST", "/register", bytes.NewBuffer(jsonBody))
			req.Header.Set("Content-Type", "application/json")
			
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)
			
			assert.Equal(t, tt.expectedStatus, w.Code)
			
			if tt.expectedError != "" {
				var response map[string]interface{}
				json.Unmarshal(w.Body.Bytes(), &response)
				assert.Contains(t, response["error"], tt.expectedError)
			}
		})
	}
}

func TestLogin(t *testing.T) {
	// Setup test database
	database.DB = setupTestDB()
	
	// Create a test user
	hashedPassword, _ := util.HashPassword("password123")
	testUser := model.User{
		Username: "logintest",
		Password: hashedPassword,
		Role:     "user",
	}
	database.DB.Create(&testUser)

	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.POST("/login", Login)

	tests := []struct {
		name           string
		requestBody    map[string]interface{}
		expectedStatus int
		expectedError  string
		checkToken     bool
	}{
		{
			name: "Valid login",
			requestBody: map[string]interface{}{
				"username": "logintest",
				"password": "password123",
			},
			expectedStatus: 200,
			checkToken:     true,
		},
		{
			name: "Invalid username",
			requestBody: map[string]interface{}{
				"username": "nonexistent",
				"password": "password123",
			},
			expectedStatus: 401,
			expectedError:  "Invalid credentials",
		},
		{
			name: "Invalid password",
			requestBody: map[string]interface{}{
				"username": "logintest",
				"password": "wrongpassword",
			},
			expectedStatus: 401,
			expectedError:  "Invalid credentials",
		},
		{
			name: "Missing fields",
			requestBody: map[string]interface{}{
				"username": "logintest",
			},
			expectedStatus: 400,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			jsonBody, _ := json.Marshal(tt.requestBody)
			req, _ := http.NewRequest("POST", "/login", bytes.NewBuffer(jsonBody))
			req.Header.Set("Content-Type", "application/json")
			
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)
			
			assert.Equal(t, tt.expectedStatus, w.Code)
			
			var response map[string]interface{}
			json.Unmarshal(w.Body.Bytes(), &response)
			
			if tt.expectedError != "" {
				assert.Contains(t, response["error"], tt.expectedError)
			}
			
			if tt.checkToken {
				assert.Contains(t, response, "token")
				assert.Contains(t, response, "user_id")
				assert.Contains(t, response, "role")
				
				// Verify the token is valid
				token, ok := response["token"].(string)
				assert.True(t, ok)
				assert.NotEmpty(t, token)
				
				claims, err := util.ValidateJWT(token)
				assert.NoError(t, err)
				assert.Equal(t, testUser.ID, claims.UID)
				assert.Equal(t, "user", claims.Role)
			}
		})
	}
}
