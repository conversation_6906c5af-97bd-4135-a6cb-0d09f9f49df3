package examples

import (
	"events-api/middleware"
	"events-api/model"
	"github.com/gin-gonic/gin"
	"net/http"
)

// ExampleRouterSetup demonstrates how to use the generic authorization middleware
// with different entity types and ownership fields
func ExampleRouterSetup() {
	router := gin.New()

	// Apply authentication middleware to all protected routes
	protected := router.Group("/")
	protected.Use(middleware.AuthMiddleware())

	// Events - using "Author" field with "user" context key
	events := protected.Group("/events")
	{
		events.POST("/", CreateEvent)
		events.GET("/", GetEvents)
		events.GET("/:id", GetEvent)
		// Using the convenience function (backward compatible)
		events.PUT("/:id", middleware.AuthorizeOwner("Author", "user"), UpdateEvent)
		events.DELETE("/:id", middleware.AuthorizeOwner("Author", "user"), DeleteEvent)
		
		// Or using the generic version explicitly
		// events.PUT("/:id", middleware.AuthorizeOwnerGeneric[model.Event]("Author", "user"), UpdateEvent)
		// events.DELETE("/:id", middleware.AuthorizeOwnerGeneric[model.Event]("Author", "user"), DeleteEvent)
	}

	// Posts - using "Owner" field with "user" context key
	posts := protected.Group("/posts")
	{
		posts.POST("/", CreatePost)
		posts.GET("/", GetPosts)
		posts.GET("/:id", GetPost)
		posts.PUT("/:id", middleware.AuthorizeOwnerGeneric[model.Post]("Owner", "user"), UpdatePost)
		posts.DELETE("/:id", middleware.AuthorizeOwnerGeneric[model.Post]("Owner", "user"), DeletePost)
	}

	// Documents - using "CreatedBy" field with "user" context key
	documents := protected.Group("/documents")
	{
		documents.POST("/", CreateDocument)
		documents.GET("/", GetDocuments)
		documents.GET("/:id", GetDocument)
		documents.PUT("/:id", middleware.AuthorizeOwnerGeneric[model.Document]("CreatedBy", "user"), UpdateDocument)
		documents.DELETE("/:id", middleware.AuthorizeOwnerGeneric[model.Document]("CreatedBy", "user"), DeleteDocument)
	}

	// Tasks - using "AssignedTo" field with "user" context key
	tasks := protected.Group("/tasks")
	{
		tasks.POST("/", CreateTask)
		tasks.GET("/", GetTasks)
		tasks.GET("/:id", GetTask)
		tasks.PUT("/:id", middleware.AuthorizeOwnerGeneric[model.Task]("AssignedTo", "user"), UpdateTask)
		tasks.DELETE("/:id", middleware.AuthorizeOwnerGeneric[model.Task]("AssignedTo", "user"), DeleteTask)
	}

	// Projects - using "Author" field with "user" context key
	projects := protected.Group("/projects")
	{
		projects.POST("/", CreateProject)
		projects.GET("/", GetProjects)
		projects.GET("/:id", GetProject)
		projects.PUT("/:id", middleware.AuthorizeOwnerGeneric[model.Project]("Author", "user"), UpdateProject)
		projects.DELETE("/:id", middleware.AuthorizeOwnerGeneric[model.Project]("Author", "user"), DeleteProject)
	}
}

// Generic handler pattern that can work with any entity type
func UpdateResource[T any](c *gin.Context) {
	// Get the resource from middleware context (already validated for ownership)
	resourceInterface, exists := c.Get("resource")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Resource not found in context"})
		return
	}

	resource, ok := resourceInterface.(T)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid resource type"})
		return
	}

	// Bind the updated data to the resource
	if err := c.ShouldBindJSON(&resource); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Save the updated resource (you would implement database save logic here)
	// database.DB.Save(&resource)
	c.JSON(http.StatusOK, resource)
}

// Example handlers for different entity types
// These are placeholder implementations - you would implement the actual logic

func CreateEvent(c *gin.Context)    { c.JSON(http.StatusCreated, gin.H{"message": "Event created"}) }
func GetEvents(c *gin.Context)      { c.JSON(http.StatusOK, gin.H{"message": "Events list"}) }
func GetEvent(c *gin.Context)       { c.JSON(http.StatusOK, gin.H{"message": "Event details"}) }
func UpdateEvent(c *gin.Context)    { UpdateResource[model.Event](c) }
func DeleteEvent(c *gin.Context)    { c.JSON(http.StatusNoContent, nil) }

func CreatePost(c *gin.Context)     { c.JSON(http.StatusCreated, gin.H{"message": "Post created"}) }
func GetPosts(c *gin.Context)       { c.JSON(http.StatusOK, gin.H{"message": "Posts list"}) }
func GetPost(c *gin.Context)        { c.JSON(http.StatusOK, gin.H{"message": "Post details"}) }
func UpdatePost(c *gin.Context)     { UpdateResource[model.Post](c) }
func DeletePost(c *gin.Context)     { c.JSON(http.StatusNoContent, nil) }

func CreateDocument(c *gin.Context) { c.JSON(http.StatusCreated, gin.H{"message": "Document created"}) }
func GetDocuments(c *gin.Context)   { c.JSON(http.StatusOK, gin.H{"message": "Documents list"}) }
func GetDocument(c *gin.Context)    { c.JSON(http.StatusOK, gin.H{"message": "Document details"}) }
func UpdateDocument(c *gin.Context) { UpdateResource[model.Document](c) }
func DeleteDocument(c *gin.Context) { c.JSON(http.StatusNoContent, nil) }

func CreateTask(c *gin.Context)     { c.JSON(http.StatusCreated, gin.H{"message": "Task created"}) }
func GetTasks(c *gin.Context)       { c.JSON(http.StatusOK, gin.H{"message": "Tasks list"}) }
func GetTask(c *gin.Context)        { c.JSON(http.StatusOK, gin.H{"message": "Task details"}) }
func UpdateTask(c *gin.Context)     { UpdateResource[model.Task](c) }
func DeleteTask(c *gin.Context)     { c.JSON(http.StatusNoContent, nil) }

func CreateProject(c *gin.Context)  { c.JSON(http.StatusCreated, gin.H{"message": "Project created"}) }
func GetProjects(c *gin.Context)    { c.JSON(http.StatusOK, gin.H{"message": "Projects list"}) }
func GetProject(c *gin.Context)     { c.JSON(http.StatusOK, gin.H{"message": "Project details"}) }
func UpdateProject(c *gin.Context)  { UpdateResource[model.Project](c) }
func DeleteProject(c *gin.Context)  { c.JSON(http.StatusNoContent, nil) }
