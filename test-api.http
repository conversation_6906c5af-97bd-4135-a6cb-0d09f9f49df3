GET http://localhost:8080/users
Accept: application/json


###
PUT http://localhost:8080/events/7
Content-Type: application/json

{
  "location": "home"
}

###
POST http://localhost:8080/events
Content-Type: application/json
Authorization: <EMAIL>

{
  "name": "nirs event",
  "description": "my desc",
  "location": "work",
  "date": "2025-09-08T12:00:00Z"
}

###
POST http://localhost:8080/events
Content-Type: application/json
Authorization: <EMAIL>

{
  "name": "2nd event",
  "description": "my desc",
  "location": "work",
  "date": "2025-09-08T12:00:00Z"
}

###
GET http://localhost:8080/events
Accept: application/json
Authorization: <EMAIL>

###
GET http://localhost:8080/profile
Accept: application/json

###
GET http://localhost:8080
Accept: application/json

###
POST http://localhost:8080/register
Content-Type: application/json

{
  "username": "<EMAIL>",
  "password": "pass"
}

###
POST http://localhost:8080/login
Content-Type: application/json

{
  "username": "<EMAIL>",
  "password": "pass"
}


