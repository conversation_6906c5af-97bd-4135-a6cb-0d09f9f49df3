package util

import (
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestGenerateJWT(t *testing.T) {
	// Test successful JWT generation
	uid := uint(123)
	role := "user"

	token, err := GenerateJWT(uid, role)
	assert.NoError(t, err)
	assert.NotEmpty(t, token)

	// Validate the generated token
	claims, err := ValidateJWT(token)
	assert.NoError(t, err)
	assert.Equal(t, uid, claims.UID)
	assert.Equal(t, role, claims.Role)
	assert.Equal(t, "events-api", claims.Issuer)
	assert.Equal(t, "user-auth", claims.Subject)
}

func TestValidateJWT(t *testing.T) {
	// Test with valid token
	uid := uint(456)
	role := "admin"
	token, err := GenerateJWT(uid, role)
	assert.NoError(t, err)

	claims, err := ValidateJWT(token)
	assert.NoError(t, err)
	assert.Equal(t, uid, claims.UID)
	assert.Equal(t, role, claims.Role)

	// Test with invalid token
	_, err = ValidateJWT("invalid.token.here")
	assert.Error(t, err)

	// Test with empty token
	_, err = ValidateJWT("")
	assert.Error(t, err)
}

func TestRefreshJWT(t *testing.T) {
	// Generate initial token
	uid := uint(789)
	role := "user"
	originalToken, err := GenerateJWT(uid, role)
	assert.NoError(t, err)

	// Wait a moment to ensure different timestamps
	time.Sleep(time.Millisecond * 10)

	// Refresh the token
	newToken, err := RefreshJWT(originalToken)
	assert.NoError(t, err)
	assert.NotEmpty(t, newToken)
	// Note: tokens might be the same if generated within the same second
	// The important thing is that both are valid

	// Validate the new token has same claims
	claims, err := ValidateJWT(newToken)
	assert.NoError(t, err)
	assert.Equal(t, uid, claims.UID)
	assert.Equal(t, role, claims.Role)

	// Test refresh with invalid token
	_, err = RefreshJWT("invalid.token")
	assert.Error(t, err)
}

func TestExtractTokenFromHeader(t *testing.T) {
	// Test valid Bearer token
	token := "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test.token"
	authHeader := "Bearer " + token

	extractedToken, err := ExtractTokenFromHeader(authHeader)
	assert.NoError(t, err)
	assert.Equal(t, token, extractedToken)

	// Test empty header
	_, err = ExtractTokenFromHeader("")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "authorization header is required")

	// Test invalid format (missing Bearer)
	_, err = ExtractTokenFromHeader(token)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "authorization header must start with 'Bearer '")

	// Test Bearer without token
	_, err = ExtractTokenFromHeader("Bearer ")
	assert.Error(t, err)
	if err != nil {
		assert.Contains(t, err.Error(), "token is required")
	}

	// Test Bearer with spaces only
	_, err = ExtractTokenFromHeader("Bearer   ")
	assert.Error(t, err)
	if err != nil {
		assert.Contains(t, err.Error(), "token is required")
	}
}

func TestJWTWithCustomSecret(t *testing.T) {
	// Set custom JWT secret
	originalSecret := os.Getenv("JWT_SECRET")
	customSecret := "test-secret-key-123"
	os.Setenv("JWT_SECRET", customSecret)
	defer func() {
		if originalSecret == "" {
			os.Unsetenv("JWT_SECRET")
		} else {
			os.Setenv("JWT_SECRET", originalSecret)
		}
	}()

	// Generate token with custom secret
	uid := uint(999)
	role := "test"
	token, err := GenerateJWT(uid, role)
	assert.NoError(t, err)

	// Validate token with same secret
	claims, err := ValidateJWT(token)
	assert.NoError(t, err)
	assert.Equal(t, uid, claims.UID)
	assert.Equal(t, role, claims.Role)
}

func TestJWTExpiration(t *testing.T) {
	// This test would require mocking time or creating a token with very short expiration
	// For now, we'll test that the expiration is set correctly
	uid := uint(111)
	role := "user"
	token, err := GenerateJWT(uid, role)
	assert.NoError(t, err)

	claims, err := ValidateJWT(token)
	assert.NoError(t, err)
	
	// Check that expiration is approximately 24 hours from now
	expectedExpiry := time.Now().Add(24 * time.Hour)
	actualExpiry := claims.ExpiresAt.Time
	
	// Allow for a few seconds difference due to execution time
	timeDiff := actualExpiry.Sub(expectedExpiry)
	assert.True(t, timeDiff < 10*time.Second && timeDiff > -10*time.Second)
}
