package util

import (
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestHashPassword(t *testing.T) {
	// Test successful password hashing
	password := "testpassword123"
	hashedPassword, err := HashPassword(password)
	
	assert.NoError(t, err)
	assert.NotEmpt<PERSON>(t, hashedPassword)
	assert.NotEqual(t, password, hashedPassword)
	assert.True(t, strings.HasPrefix(hashedPassword, "$2a$"))

	// Test that same password produces different hashes (due to salt)
	hashedPassword2, err := HashPassword(password)
	assert.NoError(t, err)
	assert.NotEqual(t, hashedPassword, hashedPassword2)

	// Test password too short
	_, err = HashPassword("12345")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "password must be at least 6 characters long")

	// Test empty password
	_, err = HashPassword("")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "password must be at least 6 characters long")
}

func TestVerifyPassword(t *testing.T) {
	password := "correctpassword"
	hashedPassword, err := HashPassword(password)
	assert.NoError(t, err)

	// Test correct password verification
	err = VerifyPassword(hashedPassword, password)
	assert.NoError(t, err)

	// Test incorrect password verification
	err = VerifyPassword(hashedPassword, "wrongpassword")
	assert.Error(t, err)

	// Test empty password verification
	err = VerifyPassword(hashedPassword, "")
	assert.Error(t, err)

	// Test verification with invalid hash
	err = VerifyPassword("invalid-hash", password)
	assert.Error(t, err)
}

func TestIsPasswordValid(t *testing.T) {
	// Test valid passwords
	validPasswords := []string{
		"password123",
		"mySecurePass",
		"123456",
		strings.Repeat("a", 127), // 127 characters
	}

	for _, password := range validPasswords {
		err := IsPasswordValid(password)
		assert.NoError(t, err, "Password should be valid: %s", password)
	}

	// Test invalid passwords
	invalidPasswords := []struct {
		password string
		errorMsg string
	}{
		{"", "password must be at least 6 characters long"},
		{"12345", "password must be at least 6 characters long"},
		{"a", "password must be at least 6 characters long"},
		{strings.Repeat("a", 129), "password must be less than 128 characters long"}, // 129 characters
	}

	for _, test := range invalidPasswords {
		err := IsPasswordValid(test.password)
		assert.Error(t, err, "Password should be invalid: %s", test.password)
		assert.Contains(t, err.Error(), test.errorMsg)
	}
}

func TestPasswordHashingIntegration(t *testing.T) {
	// Test complete flow: hash -> verify
	testCases := []string{
		"simplepass",
		"ComplexP@ssw0rd!",
		"password with spaces",
		"🔐🔑password",
		strings.Repeat("long", 15), // 60 characters (bcrypt has 72 byte limit)
	}

	for _, password := range testCases {
		// Hash the password
		hashedPassword, err := HashPassword(password)
		assert.NoError(t, err, "Failed to hash password: %s", password)

		// Verify the correct password
		err = VerifyPassword(hashedPassword, password)
		assert.NoError(t, err, "Failed to verify correct password: %s", password)

		// Verify incorrect password fails
		err = VerifyPassword(hashedPassword, password+"wrong")
		assert.Error(t, err, "Should fail to verify incorrect password: %s", password)
	}
}

func TestPasswordSecurity(t *testing.T) {
	password := "testsecurity"
	
	// Generate multiple hashes of the same password
	hashes := make([]string, 5)
	for i := 0; i < 5; i++ {
		hash, err := HashPassword(password)
		assert.NoError(t, err)
		hashes[i] = hash
	}

	// Ensure all hashes are different (due to random salt)
	for i := 0; i < len(hashes); i++ {
		for j := i + 1; j < len(hashes); j++ {
			assert.NotEqual(t, hashes[i], hashes[j], "Hashes should be different due to salt")
		}
	}

	// Ensure all hashes verify correctly
	for _, hash := range hashes {
		err := VerifyPassword(hash, password)
		assert.NoError(t, err, "All hashes should verify correctly")
	}
}
