package database

import (
	"events-api/model"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

var DB *gorm.DB

func init() {
	var err error
	DB, err = gorm.Open(sqlite.Open("test.DB"), &gorm.Config{})
	// gorm.Open("postgres", "host=localhost user=postgres password=postgres dbname=ginapp sslmode=disable")

	if err != nil {
		panic(err)
	}
	DB.AutoMigrate(&model.User{})
	DB.AutoMigrate(&model.Event{})

	// Create default admin user if it doesn't exist
	var adminUser model.User
	result := DB.Where("username = ?", "admin").First(&adminUser)
	if result.Error != nil {
		// Admin user doesn't exist, create one
		// Note: In production, you should set a secure password via environment variable
		adminUser = model.User{
			Username: "admin",
			Password: "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // password: "password"
			Role:     "admin",
		}
		DB.Create(&adminUser)
	}
}
